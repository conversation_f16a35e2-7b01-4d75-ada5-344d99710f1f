.header {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  /* background-color: #fff; */
}

.header .logo {
  display: flex;
  align-items: center;
}

.header .logo span {
  font-weight: bold;
}

.header .logo strong {
  padding: 0 6px;
  border-radius: 4px;
  font-weight: 600;
}

.header .logo img {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}

.header-flex {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Safari/iOS specific fix for toggler alignment */
@supports (-webkit-touch-callout: none) {
  .header-flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    width: 100%;
  }

  .navbar-toggler {
    margin-left: auto !important;
    position: relative;
    right: 0;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* iPhone specific hamburger menu fixes */
@media only screen and (max-width: 767px) {
  @supports (-webkit-touch-callout: none) {
    .header-flex {
      display: -webkit-box !important;
      display: -webkit-flex !important;
      display: flex !important;
      -webkit-box-pack: justify !important;
      -webkit-justify-content: space-between !important;
      justify-content: space-between !important;
      -webkit-box-align: center !important;
      -webkit-align-items: center !important;
      align-items: center !important;
      width: 100% !important;
    }

    .navbar-toggler {
      margin-left: auto !important;
      margin-right: 0 !important;
      position: relative !important;
      right: 0 !important;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      order: 2 !important;
    }

    .navbar-brand {
      order: 1 !important;
      -webkit-box-flex: 0;
      -webkit-flex: 0 0 auto;
      flex: 0 0 auto;
    }
  }
}

/* iOS Safari specific header fixes */
@supports (-webkit-touch-callout: none) {
  .navbar.fixed-top {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1030;
  }
}

/* Additional iPhone Safari specific fixes for hamburger menu */
@media screen and (max-width: 767px) and (-webkit-min-device-pixel-ratio: 1) {
  @supports (-webkit-touch-callout: none) {
    /* Force proper flexbox behavior on iPhone Safari */
    .container.container-fluid {
      padding-left: 15px !important;
      padding-right: 15px !important;
    }

    .header-flex {
      display: -webkit-box !important;
      display: -webkit-flex !important;
      display: flex !important;
      -webkit-box-orient: horizontal !important;
      -webkit-box-direction: normal !important;
      -webkit-flex-direction: row !important;
      flex-direction: row !important;
      -webkit-box-pack: justify !important;
      -webkit-justify-content: space-between !important;
      justify-content: space-between !important;
      -webkit-box-align: center !important;
      -webkit-align-items: center !important;
      align-items: center !important;
      width: 100% !important;
      min-height: 44px; /* Minimum touch target size for iOS */
    }

    .navbar-toggler {
      -webkit-appearance: none !important;
      appearance: none !important;
      background: transparent !important;
      border: none !important;
      padding: 8px !important;
      margin: 0 !important;
      margin-left: auto !important;
      position: relative !important;
      right: 0 !important;
      top: 0 !important;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      order: 999 !important; /* Ensure it's always last */
      min-width: 44px; /* iOS minimum touch target */
      min-height: 44px; /* iOS minimum touch target */
      display: -webkit-box !important;
      display: -webkit-flex !important;
      display: flex !important;
      -webkit-box-align: center !important;
      -webkit-align-items: center !important;
      align-items: center !important;
      -webkit-box-pack: center !important;
      -webkit-justify-content: center !important;
      justify-content: center !important;
    }

    .navbar-toggler-icon {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
      background-repeat: no-repeat !important;
      background-position: center !important;
      background-size: 100% !important;
      width: 1.5em !important;
      height: 1.5em !important;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }
}

/* Fallback for older iOS Safari versions */
@media screen and (max-width: 767px) {
  /* Universal fallback for all mobile Safari versions */
  .navbar-toggler {
    float: right !important;
    margin-left: auto !important;
    margin-right: 0 !important;
    position: relative !important;
    z-index: 1031 !important;
  }

  /* Ensure the header flex container works on all mobile devices */
  .header-flex {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
  }

  /* Prevent any layout shifts after login */
  .navbar-brand {
    flex-shrink: 0 !important;
  }
}

/* Specific fixes for iPhone models */
@media only screen
  and (device-width: 375px)
  and (device-height: 667px)
  and (-webkit-device-pixel-ratio: 2),
only screen
  and (device-width: 375px)
  and (device-height: 812px)
  and (-webkit-device-pixel-ratio: 3),
only screen
  and (device-width: 414px)
  and (device-height: 896px)
  and (-webkit-device-pixel-ratio: 2),
only screen
  and (device-width: 414px)
  and (device-height: 896px)
  and (-webkit-device-pixel-ratio: 3),
only screen
  and (device-width: 390px)
  and (device-height: 844px)
  and (-webkit-device-pixel-ratio: 3),
only screen
  and (device-width: 428px)
  and (device-height: 926px)
  and (-webkit-device-pixel-ratio: 3) {

  .navbar-toggler {
    position: absolute !important;
    right: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    -webkit-transform: translateY(-50%) !important;
    margin: 0 !important;
    z-index: 1031 !important;
  }

  .header-flex {
    position: relative !important;
    padding-right: 60px !important; /* Make space for the hamburger menu */
  }
}